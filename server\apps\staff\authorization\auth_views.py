from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model, authenticate
from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from datetime import timedelta
from apps.core.authentication import <PERSON><PERSON><PERSON><PERSON>uthentication
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser
from .services import PermissionService

User = get_user_model()


class StaffAccessToken(AccessToken):
    """Custom access token with staff-specific lifetime"""

    @classmethod
    def for_user(cls, user):
        token = super().for_user(user)
        # Override the token lifetime for staff users
        if user.is_staff:
            # Set a custom expiration time for staff (8 hours)
            staff_lifetime = getattr(settings, 'SIMPLE_JWT', {}).get(
                'STAFF_ACCESS_TOKEN_LIFETIME',
                timedelta(hours=8)
            )
            token.set_exp(lifetime=staff_lifetime)
        return token


class StaffRefreshToken(RefreshToken):
    """Custom refresh token with staff-specific lifetime"""
    access_token_class = StaffAccessToken

    @classmethod
    def for_user(cls, user):
        token = super().for_user(user)
        # Override the token lifetime for staff users
        if user.is_staff:
            # Set custom expiration time for staff refresh token (7 days)
            staff_refresh_lifetime = getattr(settings, 'SIMPLE_JWT', {}).get(
                'STAFF_REFRESH_TOKEN_LIFETIME',
                timedelta(days=7)
            )
            token.set_exp(lifetime=staff_refresh_lifetime)
        return token


# Note: Core app handles authentication (login/logout)
# This file contains staff-specific authorization views and staff login with custom token lifetime

class StaffLoginView(APIView):
    """
    Staff-specific login view that generates tokens with staff-appropriate lifetimes
    - Access tokens: 8 hours (instead of 90 days)
    - Refresh tokens: 7 days (instead of 90 days)
    """

    def post(self, request):
        """
        Authenticate staff user and return tokens with staff-specific lifetimes
        """
        username = request.data.get('username')
        password = request.data.get('password')

        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Authenticate user
        user = authenticate(request, username=username, password=password)

        if not user:
            return Response({
                'success': False,
                'error': 'Invalid credentials'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_active:
            return Response({
                'success': False,
                'error': 'Account is disabled'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_staff:
            return Response({
                'success': False,
                'error': 'Access denied. Staff privileges required.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Generate staff-specific tokens
        refresh = StaffRefreshToken.for_user(user)
        access_token = str(refresh.access_token)

        # Create a response with user data (no tokens in response body)
        response = Response({
            'success': True,
            'message': 'Login successful',
            'user': AuthUserSerializer(user).data
        }, status=status.HTTP_200_OK)

        # Set HTTP-only cookies for tokens
        response.set_cookie(
            'admin_access_token',
            access_token,
            max_age=8 * 60 * 60,  # 8 hours in seconds
            httponly=True,
            secure=settings.DEBUG is False,  # Secure in production
            samesite='Strict',
            path='/'
        )

        response.set_cookie(
            'admin_refresh_token',
            str(refresh),
            max_age=7 * 24 * 60 * 60,  # 7 days in seconds
            httponly=True,
            secure=settings.DEBUG is False,  # Secure in production
            samesite='Strict',
            path='/'
        )

        return response


class StaffTokenRefreshView(APIView):
    """
    Staff-specific token refresh view that refreshes HTTP-only cookies
    This endpoint is called automatically by the server when tokens are close to expiring
    """

    def post(self, request):
        """
        Refresh staff tokens from HTTP-only cookies
        """
        # Get refresh token from HTTP-only cookie
        refresh_token = request.COOKIES.get('admin_refresh_token')

        if not refresh_token:
            return Response({
                'success': False,
                'error': 'No refresh token found'
            }, status=status.HTTP_401_UNAUTHORIZED)

        try:
            # Validate the refresh token
            refresh = StaffRefreshToken(refresh_token)

            # Get the user from the token
            user = User.objects.get(id=refresh['user_id'])

            if not user.is_staff:
                return Response({
                    'success': False,
                    'error': 'Access denied. Staff privileges required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Generate new staff-specific tokens
            new_refresh = StaffRefreshToken.for_user(user)
            new_access = str(new_refresh.access_token)

            # Create a response (no tokens in body)
            response = Response({
                'success': True,
                'message': 'Tokens refreshed successfully'
            }, status=status.HTTP_200_OK)

            # Update HTTP-only cookies with new tokens
            response.set_cookie(
                'admin_access_token',
                new_access,
                max_age=8 * 60 * 60,  # 8 hours in seconds
                httponly=True,
                secure=settings.DEBUG is False,
                samesite='Strict',
                path='/'
            )

            response.set_cookie(
                'admin_refresh_token',
                str(new_refresh),
                max_age=7 * 24 * 60 * 60,  # 7 days in seconds
                httponly=True,
                secure=settings.DEBUG is False,
                samesite='Strict',
                path='/'
            )

            return response

        except Exception as e:
            # Clear cookies on refresh failure
            response = Response({
                'success': False,
                'error': 'Invalid or expired refresh token'
            }, status=status.HTTP_401_UNAUTHORIZED)

            response.delete_cookie('admin_access_token', path='/')
            response.delete_cookie('admin_refresh_token', path='/')

            return response


class StaffLogoutView(APIView):
    """
    Staff-specific logout view - clears HTTP-only cookies
    """
    authentication_classes = [StaffJWTAuthentication]
    permission_classes = [IsStaffUser]

    def post(self, request):
        """
        Logout staff user by blacklisting the refresh token from HTTP-only cookie and clearing cookies
        """
        # Get refresh token from HTTP-only cookie
        refresh_token = request.COOKIES.get('admin_refresh_token')

        if refresh_token:
            try:
                # Blacklist the refresh token
                token = StaffRefreshToken(refresh_token)
                token.blacklist()

            except Exception:
                # Token might already be blacklisted or invalid
                pass

        # Create response
        response = Response({
            'success': True,
            'message': 'Successfully logged out'
        }, status=status.HTTP_200_OK)

        # Clear authentication cookies
        response.delete_cookie('admin_access_token', path='/')
        response.delete_cookie('admin_refresh_token', path='/')

        # Also clear any legacy cookies that might exist
        response.delete_cookie('access', path='/')
        response.delete_cookie('refresh', path='/')

        return response


class CurrentUserView(APIView):
    """
    Get current authenticated staff user information with groups and permissions
    This view provides staff-specific user data including;
    - User basic information
    - Group memberships
    - Permissions (both direct and inherited from groups)
    Uses pure cookie-based authentication.
    """
    authentication_classes = [StaffJWTAuthentication]
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get current staff user information

        Returns:
            Response: JSON containing user data with groups and permissions
        """
        serializer = AuthUserSerializer(request.user)
        return Response({
            'success': True,
            'user': serializer.data
        })


class UserPermissionsView(APIView):
    """
    Get current user's permissions and groups breakdown
    This view provides detailed permission information including;
    - List of user's groups
    - All permissions (direct and inherited)
    - Group-specific permission breakdown
    - Superuser status
    """
    authentication_classes = [StaffJWTAuthentication]
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get detailed permission information for the current user

        Returns:
            Response: JSON containing detailed permission breakdown
        """
        user = request.user
        permission_service = PermissionService()

        # Get user groups
        groups = permission_service.get_user_groups(user)

        # Get all permissions
        all_permissions = permission_service.get_user_all_permissions(user)

        # Get group-specific permissions
        group_permissions = {}
        for group in user.groups.all():
            group_permissions[group.name] = list(
                group.permissions.values_list('codename', flat=True)
            )

        return Response({
            'success': True,
            'user_id': user.id,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'groups': groups,
            'permissions': all_permissions,
            'group_permissions': group_permissions
        })


class CheckPermissionView(APIView):
    """
    Check if the current user has a specific permission
    This view allows frontend to validate permissions before showing UI elements
    or making API calls that require specific permissions.
    """
    authentication_classes = [StaffJWTAuthentication]
    permission_classes = [IsStaffUser]

    def post(self, request):
        """
        Check if the user has specific permission

        Args:
            request: HTTP request containing 'permission' in data

        Returns:
            Response: JSON indicating whether the user has the permission
        """
        permission = request.data.get('permission')

        if not permission:
            return Response({
                'success': False,
                'error': 'Permission parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        permission_service = PermissionService()
        has_permission = permission_service.check_permission(request.user, permission)

        return Response({
            'success': True,
            'permission': permission,
            'has_permission': has_permission
        })


# For backward compatibility, create instances
current_user = CurrentUserView.as_view()
user_permissions = UserPermissionsView.as_view()
check_permission = CheckPermissionView.as_view()
