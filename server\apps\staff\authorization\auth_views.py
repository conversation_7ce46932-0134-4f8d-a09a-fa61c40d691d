from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model, authenticate
from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from datetime import timedelta
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser
from .services import PermissionService

User = get_user_model()


class StaffAccessToken(AccessToken):
    """Custom access token with staff-specific lifetime"""

    @classmethod
    def for_user(cls, user):
        token = super().for_user(user)
        # Override the token lifetime for staff users
        if user.is_staff:
            # Set custom expiration time for staff (8 hours)
            staff_lifetime = getattr(settings, 'SIMPLE_JWT', {}).get(
                'STAFF_ACCESS_TOKEN_LIFETIME',
                timedelta(hours=8)
            )
            token.set_exp(lifetime=staff_lifetime)
        return token


class StaffRefreshToken(RefreshToken):
    """Custom refresh token with staff-specific lifetime"""
    access_token_class = StaffAccessToken

    @classmethod
    def for_user(cls, user):
        token = super().for_user(user)
        # Override the token lifetime for staff users
        if user.is_staff:
            # Set custom expiration time for staff refresh token (7 days)
            staff_refresh_lifetime = getattr(settings, 'SIMPLE_JWT', {}).get(
                'STAFF_REFRESH_TOKEN_LIFETIME',
                timedelta(days=7)
            )
            token.set_exp(lifetime=staff_refresh_lifetime)
        return token


# Note: Core app handles authentication (login/logout)
# This file contains staff-specific authorization views and staff login with custom token lifetime

class StaffLoginView(APIView):
    """
    Staff-specific login view that generates tokens with staff-appropriate lifetimes
    - Access tokens: 8 hours (instead of 90 days)
    - Refresh tokens: 7 days (instead of 90 days)
    """

    def post(self, request):
        """
        Authenticate staff user and return tokens with staff-specific lifetimes
        """
        username = request.data.get('username')
        password = request.data.get('password')

        if not username or not password:
            return Response({
                'success': False,
                'error': 'Username and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Authenticate user
        user = authenticate(request, username=username, password=password)

        if not user:
            return Response({
                'success': False,
                'error': 'Invalid credentials'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_active:
            return Response({
                'success': False,
                'error': 'Account is disabled'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_staff:
            return Response({
                'success': False,
                'error': 'Access denied. Staff privileges required.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Generate staff-specific tokens
        refresh = StaffRefreshToken.for_user(user)
        access_token = str(refresh.access_token)

        return Response({
            'success': True,
            'access': access_token,
            'refresh': str(refresh),
            'user': AuthUserSerializer(user).data
        }, status=status.HTTP_200_OK)


class StaffTokenRefreshView(APIView):
    """
    Staff-specific token refresh view that generates new tokens with staff-appropriate lifetimes
    """

    def post(self, request):
        """
        Refresh staff tokens with staff-specific lifetimes
        """
        refresh_token = request.data.get('refresh')

        if not refresh_token:
            return Response({
                'success': False,
                'error': 'Refresh token is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Validate the refresh token
            refresh = StaffRefreshToken(refresh_token)

            # Get the user from the token
            user = User.objects.get(id=refresh['user_id'])

            if not user.is_staff:
                return Response({
                    'success': False,
                    'error': 'Access denied. Staff privileges required.'
                }, status=status.HTTP_403_FORBIDDEN)

            # Generate new staff-specific tokens
            new_refresh = StaffRefreshToken.for_user(user)
            new_access = str(new_refresh.access_token)

            return Response({
                'success': True,
                'access': new_access,
                'refresh': str(new_refresh)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'error': 'Invalid or expired refresh token'
            }, status=status.HTTP_401_UNAUTHORIZED)


class StaffLogoutView(APIView):
    """
    Staff-specific logout view
    """
    permission_classes = [IsStaffUser]

    def post(self, request):
        """
        Logout staff user by blacklisting the refresh token
        """
        refresh_token = request.data.get('refresh')

        if refresh_token:
            try:
                # Blacklist the refresh token
                token = StaffRefreshToken(refresh_token)
                token.blacklist()

            except Exception:
                # Token might already be blacklisted or invalid
                pass

        return Response({
            'success': True,
            'message': 'Successfully logged out'
        }, status=status.HTTP_200_OK)


class CurrentUserView(APIView):
    """
    Get current authenticated staff user information with groups and permissions
    This view provides staff-specific user data including;
    - User basic information
    - Group memberships
    - Permissions (both direct and inherited from groups)
    Note: The core app handles authentication, this only provides staff-specific data.
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get current staff user information

        Returns:
            Response: JSON containing user data with groups and permissions
        """
        serializer = AuthUserSerializer(request.user)
        return Response({
            'success': True,
            'user': serializer.data
        })


class UserPermissionsView(APIView):
    """
    Get current user's permissions and groups breakdown
    This view provides detailed permission information including;
    - List of user's groups
    - All permissions (direct and inherited)
    - Group-specific permission breakdown
    - Superuser status
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get detailed permission information for current user

        Returns:
            Response: JSON containing detailed permission breakdown
        """
        user = request.user
        permission_service = PermissionService()

        # Get user groups
        groups = permission_service.get_user_groups(user)

        # Get all permissions
        all_permissions = permission_service.get_user_all_permissions(user)

        # Get group-specific permissions
        group_permissions = {}
        for group in user.groups.all():
            group_permissions[group.name] = list(
                group.permissions.values_list('codename', flat=True)
            )

        return Response({
            'success': True,
            'user_id': user.id,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'groups': groups,
            'permissions': all_permissions,
            'group_permissions': group_permissions
        })


class CheckPermissionView(APIView):
    """
    Check if the current user has a specific permission
    This view allows frontend to validate permissions before showing UI elements
    or making API calls that require specific permissions.
    """
    permission_classes = [IsStaffUser]

    def post(self, request):
        """
        Check if the user has specific permission

        Args:
            request: HTTP request containing 'permission' in data

        Returns:
            Response: JSON indicating whether the user has the permission
        """
        permission = request.data.get('permission')

        if not permission:
            return Response({
                'success': False,
                'error': 'Permission parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        permission_service = PermissionService()
        has_permission = permission_service.check_permission(request.user, permission)

        return Response({
            'success': True,
            'permission': permission,
            'has_permission': has_permission
        })


# For backward compatibility, create instances
current_user = CurrentUserView.as_view()
user_permissions = UserPermissionsView.as_view()
check_permission = CheckPermissionView.as_view()
