// Pure cookie-based authentication service for staff
// All tokens are managed server-side via HTTP-only cookies
// Frontend never handles tokens directly

import { apiClient } from './api-client'
import { AuthStorage } from '../utils/auth-storage'
import {
  LoginCredentials,
  LoginResponse,
  CurrentUserResponse,
  StaffUser
} from '../types/api-types'

export class AuthService {
  /**
   * Authenticate staff user with username and password
   * Server sets HTTP-only cookies automatically
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Use staff-specific login endpoint - server sets HTTP-only cookies
      const response = await apiClient.post<{
        success: boolean
        message: string
        user: any
        error?: string
      }>('/api/staff/auth/login/', credentials)

      if (!response.data.success) {
        throw new Error(response.data.error || 'Login failed')
      }

      // Server has set HTTP-only cookies automatically
      // Return user data (no tokens in response for security)
      return {
        access: '', // Not provided in pure cookie-based auth
        refresh: '', // Not provided in pure cookie-based auth
        user: response.data.user
      }
    } catch (error: any) {
      // Handle specific login errors
      if (error.response?.status === 401) {
        throw new Error('Invalid email or password')
      } else if (error.response?.status === 403) {
        throw new Error('Account is not authorized for admin access')
      } else if (error.response?.status === 429) {
        throw new Error('Too many login attempts. Please try again later')
      } else {
        throw new Error('Login failed. Please try again')
      }
    }
  }

  /**
   * Get current authenticated user with permissions and groups
   * Authentication is handled via HTTP-only cookies
   */
  static async getCurrentUser(): Promise<CurrentUserResponse> {
    try {
      // Use staff-specific user endpoint - cookies sent automatically
      const response = await apiClient.get<{
        success: boolean
        user: any
      }>('/api/staff/auth/user/')

      if (!response.data.success) {
        throw new Error('Failed to fetch user information')
      }

      return {
        user: response.data.user,
        permissions: response.data.user.permissions || [],
        groups: response.data.user.groups || []
      }
    } catch (error: any) {
      if (error.response?.status === 401) {
        throw new Error('Authentication required')
      }
      throw new Error('Failed to fetch user information')
    }
  }

  /**
   * Check if user has specific permission
   */
  static async checkPermission(permission: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ has_permission: boolean }>('/staff/auth/check-permission/', {
        permission
      })
      return response.data.has_permission
    } catch (error) {
      console.error('Permission check failed:', error)
      return false
    }
  }

  /**
   * Get all permissions for current user
   */
  static async getUserPermissions(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ permissions: string[] }>('/staff/auth/permissions/')
      return response.data.permissions
    } catch (error) {
      console.error('Failed to fetch permissions:', error)
      return []
    }
  }

  /**
   * Get all groups for current user
   */
  static async getUserGroups(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ groups: string[] }>('/api/staff/auth/groups/')
      return response.data.groups
    } catch (error) {
      console.error('Failed to fetch groups:', error)
      return []
    }
  }

  /**
   * Refresh access token - handled automatically by server via HTTP-only cookies
   * This method is kept for compatibility but tokens are refreshed server-side
   */
  static async refreshToken(): Promise<string> {
    try {
      // Server refreshes HTTP-only cookies automatically
      const response = await apiClient.post<{
        success: boolean
        message: string
        error?: string
      }>('/staff/auth/token/refresh/')

      if (!response.data.success) {
        throw new Error(response.data.error || 'Token refresh failed')
      }

      // Server has updated HTTP-only cookies automatically
      return 'refreshed' // Placeholder since we can't access the actual token
    } catch (error: any) {
      // If refresh fails, clear any legacy cookies
      AuthStorage.clearTokens()

      if (error.response?.status === 401) {
        throw new Error('Session expired. Please login again')
      }
      throw new Error('Failed to refresh authentication')
    }
  }

  /**
   * Logout user - server clears HTTP-only cookies automatically
   */
  static async logout(): Promise<void> {
    try {
      // Server handles token blacklisting and cookie clearing
      await apiClient.post('/staff/auth/logout/')
    } catch (error) {
      // Even if server logout fails, clear any legacy cookies
      console.warn('Server logout failed, clearing legacy cookies:', error)
    } finally {
      // Clear any legacy cookies that might exist
      AuthStorage.clearTokens()
    }
  }

  /**
   * Check if user is currently authenticated
   * In pure cookie-based auth, this is determined by API responses
   */
  static isAuthenticated(): boolean {
    // Cannot determine from frontend with HTTP-only cookies
    // Authentication status is determined by API responses
    return true // Will be overridden by actual API calls
  }

  /**
   * Get basic user info from stored token (for UI purposes only)
   * Not available in pure cookie-based auth
   */
  static getUserFromToken(): null {
    // Cannot access HTTP-only cookies from JavaScript
    return null
  }

  /**
   * Check if access token is expired
   */
  static isTokenExpired(): boolean {
    return AuthStorage.isAccessTokenExpired()
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(): Date | null {
    return AuthStorage.getTokenExpiration()
  }

  /**
   * Update user profile information
   */
  static async updateProfile(data: Partial<StaffUser>): Promise<StaffUser> {
    try {
      const response = await apiClient.patch<StaffUser>('/api/staff/auth/profile/', data)
      return response.data
    } catch (error: any) {
      if (error.response?.status === 400) {
        throw new Error('Invalid profile data provided')
      }
      throw new Error('Failed to update profile')
    }
  }

  /**
   * Change user password
   */
  static async changePassword(data: {
    current_password: string
    new_password: string
    confirm_password: string
  }): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/change-password/', data)
    } catch (error: any) {
      if (error.response?.status === 400) {
        const errorData = error.response.data
        if (errorData.current_password) {
          throw new Error('Current password is incorrect')
        } else if (errorData.new_password) {
          throw new Error('New password does not meet requirements')
        } else if (errorData.confirm_password) {
          throw new Error('Password confirmation does not match')
        }
        throw new Error('Invalid password data provided')
      }
      throw new Error('Failed to change password')
    }
  }

  /**
   * Request password reset (for admin users)
   */
  static async requestPasswordReset(email: string): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/password-reset/', { email })
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('No staff account found with this email address')
      }
      throw new Error('Failed to send password reset email')
    }
  }

  /**
   * Verify password reset token
   */
  static async verifyResetToken(token: string): Promise<boolean> {
    try {
      await apiClient.post('/api/staff/auth/password-reset/verify/', { token })
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string
    new_password: string
    confirm_password: string
  }): Promise<void> {
    try {
      await apiClient.post('/api/staff/auth/password-reset/confirm/', data)
    } catch (error: any) {
      if (error.response?.status === 400) {
        const errorData = error.response.data
        if (errorData.token) {
          throw new Error('Invalid or expired reset token')
        } else if (errorData.new_password) {
          throw new Error('New password does not meet requirements')
        } else if (errorData.confirm_password) {
          throw new Error('Password confirmation does not match')
        }
        throw new Error('Invalid reset data provided')
      }
      throw new Error('Failed to reset password')
    }
  }
}
