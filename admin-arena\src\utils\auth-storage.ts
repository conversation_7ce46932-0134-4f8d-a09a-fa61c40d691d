// Cookie-based JWT storage utility
// Secure token management with proper expiration and security settings

import Cookies from 'js-cookie'
import { AuthTokens } from '../types/api-types'

interface CookieOptions {
  expires?: number | Date
  secure?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
  path?: string
}

export class AuthStorage {
  private static ACCESS_TOKEN_KEY = 'admin_access_token';
  private static REFRESH_TOKEN_KEY = 'admin_refresh_token';

  /**
   * Store authentication tokens in secure cookies
   */
  static setTokens(tokens: AuthTokens): void {
    const isProduction = import.meta.env.PROD

    // Set access token with staff-appropriate expiry (8 hours)
    const accessTokenOptions: CookieOptions = {
      expires: 8 / 24, // 8 hours (8/24 of a day)
      secure: isProduction,
      sameSite: 'strict',
      path: '/'
    }

    // Set refresh token with longer expiry (7 days)
    const refreshTokenOptions: CookieOptions = {
      expires: 7, // 7 days
      secure: isProduction,
      sameSite: 'strict',
      path: '/'
    }

    Cookies.set(this.ACCESS_TOKEN_KEY, tokens.access, accessTokenOptions)
    Cookies.set(this.REFRESH_TOKEN_KEY, tokens.refresh, refreshTokenOptions)
  }

  /**
   * Get access token from cookies
   */
  static getAccessToken(): string | null {
    return Cookies.get(this.ACCESS_TOKEN_KEY) || null
  }

  /**
   * Get refresh token from cookies
   */
  static getRefreshToken(): string | null {
    return Cookies.get(this.REFRESH_TOKEN_KEY) || null
  }

  /**
   * Clear all authentication tokens
   */
  static clearTokens(): void {
    Cookies.remove(this.ACCESS_TOKEN_KEY, { path: '/' })
    Cookies.remove(this.REFRESH_TOKEN_KEY, { path: '/' })
  }

  /**
   * Check if valid tokens exist
   */
  static hasValidTokens(): boolean {
    const accessToken = this.getAccessToken()
    const refreshToken = this.getRefreshToken()
    return !!(accessToken && refreshToken)
  }

  /**
   * Update only the access token (used during token refresh)
   */
  static updateAccessToken(accessToken: string): void {
    const isProduction = import.meta.env.PROD

    const options: CookieOptions = {
      expires: 8 / 24, // 8 hours
      secure: isProduction,
      sameSite: 'strict',
      path: '/'
    }

    Cookies.set(this.ACCESS_TOKEN_KEY, accessToken, options)
  }

  /**
   * Check if access token is expired (basic check)
   * Note: This is a simple check, actual validation should be done server-side
   */
  static isAccessTokenExpired(): boolean {
    const token = this.getAccessToken()
    if (!token) return true

    try {
      // Basic JWT payload extraction (without verification)
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp < currentTime
    } catch {
      // If we can't parse the token, consider it expired
      return true
    }
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(): Date | null {
    const token = this.getAccessToken()
    if (!token) return null

    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return new Date(payload.exp * 1000)
    } catch {
      return null
    }
  }

  /**
   * Get user information from token (without verification)
   * Note: This should only be used for UI purposes, not security decisions
   */
  static getUserFromToken(): { id: number; email: string; is_superuser: boolean } | null {
    const token = this.getAccessToken()
    if (!token) return null

    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        id: payload.user_id,
        email: payload.email,
        is_superuser: payload.is_superuser || false
      }
    } catch {
      return null
    }
  }
}
