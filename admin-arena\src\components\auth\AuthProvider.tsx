// Authentication provider component
// Handles initial auth state and automatic token refresh

import React, { useEffect } from 'react';
import { useAuth } from '../../hooks/use-auth';
import { AuthService } from '../../services/auth-service';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { getCurrentUser, isAuthenticated } = useAuth();

  useEffect(() => {
    // Check if user is authenticated on app load
    const initializeAuth = async () => {
      if (AuthService.isAuthenticated() && !isAuthenticated) {
        try {
          await getCurrentUser();
        } catch (error) {
          console.error('Failed to initialize auth:', error);
          // If auth fails, the auth store will handle clearing tokens
        }
      }
    };

    initializeAuth();
  }, [getCurrentUser, isAuthenticated]);

  // Set up token refresh interval
  useEffect(() => {
    if (!isAuthenticated) return;

    const refreshInterval = setInterval(async () => {
      try {
        // Check if token is close to expiring (within 5 minutes)
        const expiration = AuthService.getTokenExpiration();
        if (expiration) {
          const timeUntilExpiry = expiration.getTime() - Date.now();
          const fiveMinutes = 5 * 60 * 1000;
          
          if (timeUntilExpiry <= fiveMinutes) {
            await AuthService.refreshToken();
          }
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        // The auth service will handle clearing tokens on failure
      }
    }, 60000); // Check every minute

    return () => clearInterval(refreshInterval);
  }, [isAuthenticated]);

  return <>{children}</>;
};
