// Dashboard home route
// Main landing page for authenticated admin users

import { createFileRoute, redirect } from '@tanstack/react-router'
import { DashboardPage } from '../pages/dashboard/DashboardPage'
import { AuthService } from '../services/auth-service'

export const Route = createFileRoute('/')({
  beforeLoad: () => {
    // Redirect to login if not authenticated
    if (!AuthService.isAuthenticated()) {
      throw redirect({ to: '/login' })
    }
  },
  component: DashboardPage,
})
